<script setup lang="ts">
// 方案变更-全单服务费方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

import { QuestionCircleOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  schemeInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  cacheServiceFee: {
    type: Object,
    default: {},
  },
  materialPrice: {
    // 布展物料价格
    type: Number,
    default: 0,
  },
  presentPrice: {
    // 礼品价格
    type: Number,
    default: 0,
  },
  otherPrice: {
    // 其他价格
    type: Number,
    default: 0,
  },
  planEachPriceList: {
    // 全单服务费配置项
    type: Array,
    default: [],
  },
  fullServiceRangeRateLimit: {
    // 全单服务费收取比例
    type: Number,
    default: 0,
  },
  fullServiceRemark: {
    type: String,
    default: '',
  },
  serviceFeeSets: {
    // 全单服务费配置项
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['schemeFeeEmit']);

const serviceFeeRate = ref<number>(null);
const schemeTotal = ref<number>(0);

// 收取范围
const feeRangeName = () => {
  let names = [];

  props.serviceFeeSets.forEach((e) => {
    if (e === 1 && props.schemeInfo.stays && props.schemeInfo.stays.length > 0) names.push('住宿');
    if (e === 2 && props.schemeInfo.places && props.schemeInfo.places.length > 0) names.push('会场');
    if (e === 4 && props.schemeInfo.caterings && props.schemeInfo.caterings.length > 0) names.push('用餐');
    if (e === 8 && props.schemeInfo.vehicles && props.schemeInfo.vehicles.length > 0) names.push('用车');
    if (e === 16 && props.schemeInfo.attendants && props.schemeInfo.attendants.length > 0) names.push('服务人员');
    if (e === 32 && props.schemeInfo.activities && props.schemeInfo.activities.length > 0) names.push('拓展活动');
    if (e === 64 && props.schemeInfo.insurances && props.schemeInfo.insurances.length > 0) names.push('保险');
    if (
      e === 128 &&
      props.schemeInfo.material &&
      props.schemeInfo.material.materialDetails &&
      props.schemeInfo.material.materialDetails.length > 0
    )
      names.push('布展物料');
    if (e === 256 && props.schemeInfo.traffic && Object.keys(props.schemeInfo.traffic).length > 0) names.push('交通');
    if (e === 512 && props.schemeInfo.presents && props.schemeInfo.presents.length > 0) names.push('礼品');
    if (e === 1024 && props.schemeInfo.others && props.schemeInfo.others.length > 0) names.push('其他');
  });

  const nameStr = names.join('、') || '-';
  return nameStr;
};

// 价格计算
const getPrice = (type: number) => {
  let priceItem = 0;

  // 住宿、会场相关
  const arr = props.planEachPriceList.filter((e) => e.type === type) || [];

  priceItem = arr[0]?.price || 0;

  return priceItem;
};

// 暂存
const serviceFeeTempSave = () => {
  schemeTotal.value = 0;

  props.serviceFeeSets.forEach((e) => {
    schemeTotal.value += getPrice(e);
  });

  if (props.serviceFeeSets.includes(128)) {
    // 布展物料
    schemeTotal.value += props.materialPrice;
  }
  if (props.serviceFeeSets.includes(256)) {
    // 交通
    // schemeTotal.value += props.materialPrice;
  }
  if (props.serviceFeeSets.includes(512)) {
    // 礼品
    schemeTotal.value += props.presentPrice;
  }
  if (props.serviceFeeSets.includes(1024)) {
    // 其他
    schemeTotal.value += props.otherPrice;
  }

  emit('schemeFeeEmit', {
    serviceFee: {
      id: props.cacheServiceFee.id,
      serviceFeeLimitRate: props.fullServiceRangeRateLimit, // 服务商比例上限
      serviceFeeLimit: (schemeTotal.value * props.fullServiceRangeRateLimit) / 100, // 服务商对应上限金额
      schemeServiceFeeReal: (schemeTotal.value * serviceFeeRate.value) / 100, // 实际服务费
      serviceFeeRate: serviceFeeRate.value,
    },
  });
};

watch(
  () => props.cacheServiceFee,
  () => {
    // 实际服务费
    serviceFeeRate.value =
      props.cacheServiceFee.serviceFeeRate !== null &&
      props.cacheServiceFee.serviceFeeRate !== 'null' &&
      props.cacheServiceFee.serviceFeeRate !== undefined &&
      props.cacheServiceFee.serviceFeeRate !== 'undefined'
        ? props.cacheServiceFee.serviceFeeRate
        : null;

    serviceFeeTempSave();
  },
  {
    immediate: true,
    deep: true,
  },
);

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 校验
const serviceFeeSub = () => {
  let isVerPassed = true;

  if (serviceFeeRate.value === null || serviceFeeRate.value === undefined) {
    message.error('请输入全单服务费！');

    isVerPassed = false;
    anchorJump('schemeServiceFeeId');
    return;
  }

  if (isVerPassed) {
    serviceFeeTempSave();
  }

  return isVerPassed;
};

defineExpose({ serviceFeeSub, serviceFeeTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 全单服务费方案 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>全单服务费方案</span>

      <a-tooltip class="ml10" v-if="props.fullServiceRemark">
        <template #title>
          {{ props.fullServiceRemark }}
        </template>
        <QuestionCircleOutlined />
      </a-tooltip>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_table">
          <div class="scheme_plan_index">全单服务费</div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label">收取比例上限</div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              {{
                props.fullServiceRangeRateLimit !== null && props.fullServiceRangeRateLimit !== undefined
                  ? props.fullServiceRangeRateLimit + '%'
                  : '-'
              }}
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_table">
          <div class="scheme_plan_index">全单服务费</div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label">收取比例</div>
            <div class="scheme_plan_label">收取范围</div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip
                placement="topLeft"
                v-if="props.schemeChangeType === 'biddingView' || props.schemeChangeType === 'schemeView'"
              >
                <template #title>
                  {{ serviceFeeRate ? formatNumberThousands(serviceFeeRate) + '%' : '-' }}
                </template>
                {{ serviceFeeRate ? formatNumberThousands(serviceFeeRate) + '%' : '-' }}
              </a-tooltip>
              <div v-else>
                <a-input-number
                  style="width: calc(100% - 44px)"
                  v-model:value="serviceFeeRate"
                  @change="serviceFeeTempSave"
                  placeholder="请输入"
                  :bordered="false"
                  allow-clear
                  :min="0"
                  :max="props.fullServiceRangeRateLimit"
                  :precision="2"
                />
                <span>%</span>
                <div class="scheme_plan_edit"></div>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ feeRangeName() }}
                </template>
                {{ feeRangeName() }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12" :id="'schemeServiceFeeId'">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">服务费：</div>
              <div class="scheme_plan_price_value">
                {{
                  schemeTotal && serviceFeeRate !== null
                    ? '¥' + formatNumberThousands((schemeTotal * serviceFeeRate) / 100)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">金额：</div>
              <div class="scheme_plan_price_value">
                {{ schemeTotal ? '¥' + formatNumberThousands(schemeTotal) : '-' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_material.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_index {
    width: 90px !important;
  }
  .scheme_plan_list1 {
    width: 110px !important;
  }
  .scheme_plan_list2 {
    width: calc(100% - 90px - 110px - 190px) !important;
  }

  .scheme_plan_list3 {
    width: 180px !important;
  }

  .scheme_plan_value {
    border-bottom: 1px solid #e5e6eb;

    /* 住宿人数 */
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .pr0 {
    padding-right: 0 !important;
  }
}
</style>
